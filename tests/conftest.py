"""
Shared test configuration and fixtures for Invoice Generator tests.

This module contains common test fixtures, configuration, and utilities
that can be used across all test modules.
"""
import os
import tempfile
from datetime import datetime, timedelta


# Test configuration constants
TEST_CONFIG = {
    'currency': {
        'code': 'EUR',
        'symbol': '€'
    },
    'issuer': {
        'name': 'Test Issuer',
        'tax_id': 'TEST123456',
        'address': 'Test Address 123',
        'city': 'Test City',
        'country': 'Test Country',
        'phone': '+34 ***********',
        'email': '<EMAIL>',
        'bank_iban': 'ES12 3456 7890 1234 5678 9012',
        'bank_name': 'Test Bank'
    },
    'preferences': {
        'last_client_id': None,
        'last_service_id': None
    },
    'tax_rates': {
        'default_iva': 0.21,
        'default_irpf': 0.15
    }
}

# Test data fixtures
TEST_CLIENTS = [
    {
        'name': 'Test Client One LLC',
        'tax_id': 'TC001',
        'address': '123 First Street',
        'country': 'Spain',
        'email': '<EMAIL>',
        'currency_code': 'EUR',
        'currency_symbol': '€'
    },
    {
        'name': 'Test Client Two Inc',
        'tax_id': 'TC002',
        'address': '456 Second Avenue',
        'country': 'USA',
        'email': '<EMAIL>',
        'currency_code': 'USD',
        'currency_symbol': '$'
    },
    {
        'name': 'Test Client Three Ltd',
        'tax_id': 'TC003',
        'address': '789 Third Boulevard',
        'country': 'UK',
        'email': '<EMAIL>',
        'currency_code': 'GBP',
        'currency_symbol': '£'
    }
]

TEST_SERVICES = [
    {
        'description': 'Software Development',
        'unit_price': 75.0,
        'unit_type': 'hour'
    },
    {
        'description': 'Technical Consulting',
        'unit_price': 100.0,
        'unit_type': 'hour'
    },
    {
        'description': 'Project Management',
        'unit_price': 85.0,
        'unit_type': 'hour'
    },
    {
        'description': 'Monthly Maintenance',
        'unit_price': 500.0,
        'unit_type': 'month'
    }
]

# Test calculation scenarios
CALCULATION_TEST_CASES = [
    {
        'name': 'standard_with_both_taxes',
        'subtotal': 100.0,
        'apply_iva': True,
        'apply_irpf': True,
        'expected_iva': 21.0,
        'expected_irpf': 15.0,
        'expected_total': 106.0
    },
    {
        'name': 'no_iva_with_irpf',
        'subtotal': 100.0,
        'apply_iva': False,
        'apply_irpf': True,
        'expected_iva': 0.0,
        'expected_irpf': 15.0,
        'expected_total': 85.0
    },
    {
        'name': 'with_iva_no_irpf',
        'subtotal': 100.0,
        'apply_iva': True,
        'apply_irpf': False,
        'expected_iva': 21.0,
        'expected_irpf': 0.0,
        'expected_total': 121.0
    },
    {
        'name': 'no_taxes',
        'subtotal': 100.0,
        'apply_iva': False,
        'apply_irpf': False,
        'expected_iva': 0.0,
        'expected_irpf': 0.0,
        'expected_total': 100.0
    },
    {
        'name': 'decimal_amounts',
        'subtotal': 123.45,
        'apply_iva': True,
        'apply_irpf': True,
        'expected_iva': 25.92,  # 123.45 * 0.21 = 25.9245 -> 25.92
        'expected_irpf': 18.52,  # 123.45 * 0.15 = 18.5175 -> 18.52
        'expected_total': 130.85  # 123.45 + 25.92 - 18.52 = 130.85
    }
]

# Date utilities for testing
def get_test_date(days_offset=0):
    """Get a test date with optional offset from today."""
    return (datetime.now() + timedelta(days=days_offset)).strftime('%Y-%m-%d')

def get_test_date_formatted(days_offset=0):
    """Get a test date in DD/MM/YYYY format with optional offset."""
    return (datetime.now() + timedelta(days=days_offset)).strftime('%d/%m/%Y')

# Mock data generators
def generate_test_invoice_data(client_id=1, service_id=1, quantity=1):
    """Generate test data for invoice creation."""
    return {
        'client_id': client_id,
        'service_id': service_id,
        'quantity': quantity,
        'invoice_date': get_test_date(),
        'apply_iva': True,
        'apply_irpf': True
    }

def generate_test_estimate_data(client_id=1, service_id=1, quantity=1):
    """Generate test data for estimate creation."""
    return {
        'client_id': client_id,
        'service_id': service_id,
        'quantity': quantity,
        'issue_date': get_test_date(),
        'valid_until_date': get_test_date(30),  # 30 days from now
        'irpf_rate': 0.15,
        'notes': 'Test estimate notes',
        'terms': 'Test estimate terms'
    }

# Environment setup utilities
def setup_test_environment():
    """Set up a clean test environment."""
    # This function can be called at the beginning of test suites
    # to ensure a consistent starting state
    pass

def cleanup_test_environment():
    """Clean up test environment."""
    # This function can be called at the end of test suites
    # to clean up any remaining test artifacts
    pass
