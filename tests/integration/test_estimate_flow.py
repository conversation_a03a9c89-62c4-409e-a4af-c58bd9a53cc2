"""
Integration tests for estimate generation workflow in the Invoice Generator.

These tests verify the complete estimate creation process from data input
to database storage and retrieval.
"""
import unittest
from datetime import datetime
from tests.test_base import TestBase, DatabaseTestMixin
from tests.conftest import generate_test_estimate_data
from src.models.estimates import save_estimate, get_estimate_by_number, generate_estimate_number
from src.models.clients import add_client
from src.models.services import add_service


class EstimateFlowTests(TestBase, DatabaseTestMixin):
    """Tests for complete estimate generation workflow."""
    
    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        
        # Create test client and service
        self.client_id = self.create_test_client()
        self.service_id = self.create_test_service()
        
        # Verify they were created
        self.assertIsNotNone(self.client_id, "Test client should be created")
        self.assertIsNotNone(self.service_id, "Test service should be created")
    
    def test_estimate_number_generation(self):
        """Test estimate number generation with proper format."""
        # Arrange
        issue_date = datetime.now()
        
        # Act
        estimate_number = generate_estimate_number(issue_date)
        
        # Assert
        self.assertIsNotNone(estimate_number, "Estimate number should not be None")
        self.assertIsInstance(estimate_number, str, "Estimate number should be string")
        
        # Check format: YYYY-MM-PXXX
        parts = estimate_number.split('-')
        self.assertEqual(len(parts), 3, f"Estimate number format incorrect: {estimate_number}")
        
        # Verify year and month
        expected_year_month = issue_date.strftime("%Y-%m")
        actual_year_month = f"{parts[0]}-{parts[1]}"
        self.assertEqual(actual_year_month, expected_year_month, 
                        f"Year-month part incorrect: expected {expected_year_month}, got {actual_year_month}")
        
        # Verify sequence part starts with 'P' and has numbers
        sequence_part = parts[2]
        self.assertTrue(sequence_part.startswith('P'), f"Sequence part should start with 'P': {sequence_part}")
        self.assertTrue(sequence_part[1:].isdigit(), f"Sequence part should contain digits: {sequence_part}")
    
    def test_save_estimate_complete_workflow(self):
        """Test complete estimate saving workflow with all calculations."""
        # Arrange
        estimate_data = generate_test_estimate_data(self.client_id, self.service_id, quantity=2)
        issue_date_obj = datetime.strptime(estimate_data['issue_date'], '%Y-%m-%d')
        estimate_number = generate_estimate_number(issue_date_obj)
        
        # Act
        result = save_estimate(
            client_id=estimate_data['client_id'],
            service_id=estimate_data['service_id'],
            quantity=estimate_data['quantity'],
            issue_date_str=estimate_data['issue_date'],
            valid_until_date_str=estimate_data['valid_until_date'],
            irpf_rate_decimal=estimate_data['irpf_rate'],
            notes=estimate_data['notes'],
            terms=estimate_data['terms'],
            estimate_number_override=estimate_number
        )
        
        # Assert
        self.assertEqual(result, estimate_number, "Should return the estimate number")
        
        # Verify estimate was saved to database
        self.assertTableExists('estimates')
        
        # Check that estimate record exists
        saved_estimate = get_estimate_by_number(estimate_number)
        self.assertIsNotNone(saved_estimate, "Estimate should be retrievable from database")
    
    def test_estimate_retrieval_with_complete_data(self):
        """Test estimate retrieval with all related data (client, service)."""
        # Arrange - Create and save an estimate
        estimate_data = generate_test_estimate_data(self.client_id, self.service_id, quantity=1)
        issue_date_obj = datetime.strptime(estimate_data['issue_date'], '%Y-%m-%d')
        estimate_number = generate_estimate_number(issue_date_obj)
        
        save_estimate(
            client_id=estimate_data['client_id'],
            service_id=estimate_data['service_id'],
            quantity=estimate_data['quantity'],
            issue_date_str=estimate_data['issue_date'],
            valid_until_date_str=estimate_data['valid_until_date'],
            irpf_rate_decimal=estimate_data['irpf_rate'],
            notes=estimate_data['notes'],
            terms=estimate_data['terms'],
            estimate_number_override=estimate_number
        )
        
        # Act
        retrieved_estimate = get_estimate_by_number(estimate_number)
        
        # Assert
        self.assertIsNotNone(retrieved_estimate, "Estimate should be retrievable")
        
        # Verify estimate data
        self.assertEqual(retrieved_estimate['estimate_number'], estimate_number)
        self.assertEqual(retrieved_estimate['client_id'], self.client_id)
        self.assertEqual(retrieved_estimate['service_id'], self.service_id)
        self.assertEqual(retrieved_estimate['quantity'], estimate_data['quantity'])
        self.assertEqual(retrieved_estimate['issue_date'], estimate_data['issue_date'])
        self.assertEqual(retrieved_estimate['valid_until_date'], estimate_data['valid_until_date'])
        self.assertEqual(retrieved_estimate['irpf_rate'], estimate_data['irpf_rate'])
        self.assertEqual(retrieved_estimate['notes'], estimate_data['notes'])
        self.assertEqual(retrieved_estimate['terms'], estimate_data['terms'])
        self.assertEqual(retrieved_estimate['status'], 'Draft')
        
        # Verify client data is included
        self.assertIn('client_name', retrieved_estimate)
        self.assertEqual(retrieved_estimate['client_name'], 'Test Client LLC')
        
        # Verify service data is included
        self.assertIn('service_description', retrieved_estimate)
        self.assertEqual(retrieved_estimate['service_description'], 'Test Service')
        
        # Verify financial calculations
        self.assertIn('subtotal', retrieved_estimate)
        self.assertIn('iva_amount', retrieved_estimate)
        self.assertIn('irpf_amount', retrieved_estimate)
        self.assertIn('total', retrieved_estimate)
        
        # Verify calculation correctness (100.0 * 1 = 100.0 subtotal)
        expected_subtotal = 100.0
        expected_iva = 21.0  # 100 * 0.21
        expected_irpf = 15.0  # 100 * 0.15
        expected_total = 106.0  # 100 + 21 - 15
        
        self.assertAlmostEqual(retrieved_estimate['subtotal'], expected_subtotal, places=2)
        self.assertAlmostEqual(retrieved_estimate['iva_amount'], expected_iva, places=2)
        self.assertAlmostEqual(retrieved_estimate['irpf_amount'], expected_irpf, places=2)
        self.assertAlmostEqual(retrieved_estimate['total'], expected_total, places=2)
    
    def test_estimate_financial_calculations(self):
        """Test that estimate financial calculations are correct."""
        # Arrange
        quantity = 2.5  # 2.5 hours
        service_price = 75.0  # €75/hour
        expected_subtotal = 187.5  # 75 * 2.5
        
        estimate_data = generate_test_estimate_data(self.client_id, self.service_id, quantity=quantity)
        issue_date_obj = datetime.strptime(estimate_data['issue_date'], '%Y-%m-%d')
        estimate_number = generate_estimate_number(issue_date_obj)
        
        # Act
        save_estimate(
            client_id=estimate_data['client_id'],
            service_id=estimate_data['service_id'],
            quantity=quantity,
            issue_date_str=estimate_data['issue_date'],
            valid_until_date_str=estimate_data['valid_until_date'],
            irpf_rate_decimal=estimate_data['irpf_rate'],
            notes=estimate_data['notes'],
            terms=estimate_data['terms'],
            estimate_number_override=estimate_number
        )
        
        retrieved_estimate = get_estimate_by_number(estimate_number)
        
        # Assert
        self.assertAlmostEqual(retrieved_estimate['subtotal'], expected_subtotal, places=2)
        
        # Calculate expected values
        expected_iva = round(expected_subtotal * 0.21, 2)  # 21% IVA
        expected_irpf = round(expected_subtotal * 0.15, 2)  # 15% IRPF
        expected_total = round(expected_subtotal + expected_iva - expected_irpf, 2)
        
        self.assertAlmostEqual(retrieved_estimate['iva_amount'], expected_iva, places=2)
        self.assertAlmostEqual(retrieved_estimate['irpf_amount'], expected_irpf, places=2)
        self.assertAlmostEqual(retrieved_estimate['total'], expected_total, places=2)
    
    def test_estimate_with_different_currencies(self):
        """Test estimate creation with different client currencies."""
        # Arrange - Create USD client
        usd_client_data = self.get_test_client_data()
        usd_client_data.update({
            'name': 'USD Test Client',
            'currency_code': 'USD',
            'currency_symbol': '$'
        })
        usd_client_id = add_client(**usd_client_data)
        
        estimate_data = generate_test_estimate_data(usd_client_id, self.service_id)
        issue_date_obj = datetime.strptime(estimate_data['issue_date'], '%Y-%m-%d')
        estimate_number = generate_estimate_number(issue_date_obj)
        
        # Act
        save_estimate(
            client_id=estimate_data['client_id'],
            service_id=estimate_data['service_id'],
            quantity=estimate_data['quantity'],
            issue_date_str=estimate_data['issue_date'],
            valid_until_date_str=estimate_data['valid_until_date'],
            irpf_rate_decimal=estimate_data['irpf_rate'],
            notes=estimate_data['notes'],
            terms=estimate_data['terms'],
            estimate_number_override=estimate_number
        )
        
        retrieved_estimate = get_estimate_by_number(estimate_number)
        
        # Assert
        self.assertEqual(retrieved_estimate['currency'], 'USD')
        self.assertEqual(retrieved_estimate['client_currency_symbol'], '$')
    
    def test_multiple_estimates_sequential_numbering(self):
        """Test that multiple estimates get sequential numbering."""
        # Arrange
        issue_date = datetime.now()
        
        # Act - Generate multiple estimate numbers
        estimate_number_1 = generate_estimate_number(issue_date)
        estimate_number_2 = generate_estimate_number(issue_date)
        estimate_number_3 = generate_estimate_number(issue_date)
        
        # Assert - Numbers should be sequential
        base_format = issue_date.strftime("%Y-%m-P")
        
        self.assertTrue(estimate_number_1.startswith(base_format))
        self.assertTrue(estimate_number_2.startswith(base_format))
        self.assertTrue(estimate_number_3.startswith(base_format))
        
        # Extract sequence numbers
        seq_1 = int(estimate_number_1.split('-P')[1])
        seq_2 = int(estimate_number_2.split('-P')[1])
        seq_3 = int(estimate_number_3.split('-P')[1])
        
        # Should be sequential
        self.assertEqual(seq_2, seq_1 + 1, "Second estimate should have next sequence number")
        self.assertEqual(seq_3, seq_2 + 1, "Third estimate should have next sequence number")


if __name__ == '__main__':
    unittest.main()
