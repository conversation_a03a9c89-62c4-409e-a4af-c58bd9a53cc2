"""
Unit tests for financial calculation functions in the Invoice Generator.

These tests verify the correctness of IVA, IRPF, and total calculations
under various scenarios and edge cases.
"""
import unittest
from tests.test_base import TestBase
from tests.conftest import CALCULATION_TEST_CASES
from src import utils


class CalculationTests(TestBase):
    """Tests for financial calculation functions."""
    
    def test_calculate_financials_with_standard_rates(self):
        """Test calculate_financials with standard IVA and IRPF rates."""
        # Arrange
        subtotal = 100.0
        iva_rate = 0.21
        irpf_rate = 0.15
        
        # Act
        result = utils.calculate_financials(subtotal, iva_rate, irpf_rate)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 100.0)
        self.assertFloatEqual(result['iva_amount'], 21.0)
        self.assertFloatEqual(result['irpf_amount'], 15.0)
        self.assertFloatEqual(result['total'], 106.0)
        
        # Verify all expected keys are present
        expected_keys = ['subtotal', 'iva_amount', 'irpf_amount', 'total']
        for key in expected_keys:
            self.assertIn(key, result, f"Missing key '{key}' in result")
    
    def test_calculate_financials_with_zero_rates(self):
        """Test calculate_financials with zero tax rates."""
        # Arrange
        subtotal = 150.0
        iva_rate = 0.0
        irpf_rate = 0.0
        
        # Act
        result = utils.calculate_financials(subtotal, iva_rate, irpf_rate)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 150.0)
        self.assertFloatEqual(result['iva_amount'], 0.0)
        self.assertFloatEqual(result['irpf_amount'], 0.0)
        self.assertFloatEqual(result['total'], 150.0)
    
    def test_calculate_financials_with_decimal_amounts(self):
        """Test calculate_financials with decimal amounts for precision."""
        # Arrange
        subtotal = 123.45
        iva_rate = 0.21
        irpf_rate = 0.15
        
        # Act
        result = utils.calculate_financials(subtotal, iva_rate, irpf_rate)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 123.45)
        self.assertFloatEqual(result['iva_amount'], 25.92)  # 123.45 * 0.21 = 25.9245 -> 25.92
        self.assertFloatEqual(result['irpf_amount'], 18.52)  # 123.45 * 0.15 = 18.5175 -> 18.52
        self.assertFloatEqual(result['total'], 130.85)  # 123.45 + 25.92 - 18.52 = 130.85
    
    def test_calculate_invoice_totals_with_iva_and_irpf(self):
        """Test calculate_invoice_totals with both IVA and IRPF applied."""
        # Arrange
        service_price = 100.0
        quantity = 2
        apply_iva = True
        apply_irpf = True
        
        # Act
        result = utils.calculate_invoice_totals(service_price, quantity, apply_iva, apply_irpf)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 200.0)  # 100 * 2
        self.assertFloatEqual(result['iva_amount'], 42.0)  # 200 * 0.21
        self.assertFloatEqual(result['irpf_amount'], 30.0)  # 200 * 0.15
        self.assertFloatEqual(result['total'], 212.0)  # 200 + 42 - 30
    
    def test_calculate_invoice_totals_without_iva(self):
        """Test calculate_invoice_totals with IVA disabled."""
        # Arrange
        service_price = 100.0
        quantity = 1
        apply_iva = False
        apply_irpf = True
        
        # Act
        result = utils.calculate_invoice_totals(service_price, quantity, apply_iva, apply_irpf)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 100.0)
        self.assertFloatEqual(result['iva_amount'], 0.0)
        self.assertFloatEqual(result['irpf_amount'], 15.0)
        self.assertFloatEqual(result['total'], 85.0)
    
    def test_calculate_invoice_totals_without_irpf(self):
        """Test calculate_invoice_totals with IRPF disabled."""
        # Arrange
        service_price = 100.0
        quantity = 1
        apply_iva = True
        apply_irpf = False
        
        # Act
        result = utils.calculate_invoice_totals(service_price, quantity, apply_iva, apply_irpf)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 100.0)
        self.assertFloatEqual(result['iva_amount'], 21.0)
        self.assertFloatEqual(result['irpf_amount'], 0.0)
        self.assertFloatEqual(result['total'], 121.0)
    
    def test_calculate_invoice_totals_without_taxes(self):
        """Test calculate_invoice_totals with both taxes disabled."""
        # Arrange
        service_price = 100.0
        quantity = 1
        apply_iva = False
        apply_irpf = False
        
        # Act
        result = utils.calculate_invoice_totals(service_price, quantity, apply_iva, apply_irpf)
        
        # Assert
        self.assertFloatEqual(result['subtotal'], 100.0)
        self.assertFloatEqual(result['iva_amount'], 0.0)
        self.assertFloatEqual(result['irpf_amount'], 0.0)
        self.assertFloatEqual(result['total'], 100.0)
    
    def test_calculation_scenarios_from_config(self):
        """Test multiple calculation scenarios using test configuration data."""
        for case in CALCULATION_TEST_CASES:
            with self.subTest(scenario=case['name']):
                # Arrange
                subtotal = case['subtotal']
                apply_iva = case['apply_iva']
                apply_irpf = case['apply_irpf']
                
                # Act
                result = utils.calculate_invoice_totals(subtotal, 1, apply_iva, apply_irpf)
                
                # Assert
                self.assertFloatEqual(result['subtotal'], subtotal, 
                                    msg=f"Subtotal mismatch in scenario '{case['name']}'")
                self.assertFloatEqual(result['iva_amount'], case['expected_iva'],
                                    msg=f"IVA amount mismatch in scenario '{case['name']}'")
                self.assertFloatEqual(result['irpf_amount'], case['expected_irpf'],
                                    msg=f"IRPF amount mismatch in scenario '{case['name']}'")
                self.assertFloatEqual(result['total'], case['expected_total'],
                                    msg=f"Total amount mismatch in scenario '{case['name']}'")
    
    def test_calculate_financials_return_types(self):
        """Test that calculate_financials returns correct data types."""
        # Arrange
        subtotal = 100.0
        iva_rate = 0.21
        irpf_rate = 0.15
        
        # Act
        result = utils.calculate_financials(subtotal, iva_rate, irpf_rate)
        
        # Assert
        self.assertIsInstance(result, dict, "Result should be a dictionary")
        self.assertIsInstance(result['subtotal'], float, "Subtotal should be float")
        self.assertIsInstance(result['iva_amount'], float, "IVA amount should be float")
        self.assertIsInstance(result['irpf_amount'], float, "IRPF amount should be float")
        self.assertIsInstance(result['total'], float, "Total should be float")
    
    def test_calculate_invoice_totals_with_fractional_quantity(self):
        """Test calculate_invoice_totals with fractional quantities."""
        # Arrange
        service_price = 75.0
        quantity = 2.5  # 2.5 hours
        apply_iva = True
        apply_irpf = True
        
        # Act
        result = utils.calculate_invoice_totals(service_price, quantity, apply_iva, apply_irpf)
        
        # Assert
        expected_subtotal = 187.5  # 75 * 2.5
        expected_iva = 39.38  # 187.5 * 0.21 = 39.375 -> 39.38
        expected_irpf = 28.13  # 187.5 * 0.15 = 28.125 -> 28.13
        expected_total = 198.75  # 187.5 + 39.38 - 28.13 = 198.75
        
        self.assertFloatEqual(result['subtotal'], expected_subtotal)
        self.assertFloatEqual(result['iva_amount'], expected_iva)
        self.assertFloatEqual(result['irpf_amount'], expected_irpf)
        self.assertFloatEqual(result['total'], expected_total)
    
    def test_rounding_precision(self):
        """Test that calculations maintain proper rounding precision."""
        # Arrange - Use amounts that will test rounding edge cases
        subtotal = 33.33
        iva_rate = 0.21
        irpf_rate = 0.15
        
        # Act
        result = utils.calculate_financials(subtotal, iva_rate, irpf_rate)
        
        # Assert - All amounts should be rounded to 2 decimal places
        self.assertEqual(len(str(result['subtotal']).split('.')[-1]), 2, 
                        "Subtotal should have 2 decimal places")
        self.assertEqual(len(str(result['iva_amount']).split('.')[-1]), 2,
                        "IVA amount should have 2 decimal places")
        self.assertEqual(len(str(result['irpf_amount']).split('.')[-1]), 2,
                        "IRPF amount should have 2 decimal places")
        self.assertEqual(len(str(result['total']).split('.')[-1]), 2,
                        "Total should have 2 decimal places")


if __name__ == '__main__':
    unittest.main()
