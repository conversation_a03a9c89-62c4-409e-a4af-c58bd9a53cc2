# Invoice Generator Tests

This directory contains comprehensive test modules for the Invoice Generator application.

## Test Structure

```
tests/
├── README.md                    # This file
├── __init__.py                  # Test package initialization
├── conftest.py                  # Shared test configuration and fixtures
├── test_base.py                 # Base test class with common utilities
├── unit/                        # Unit tests
│   ├── __init__.py
│   ├── test_models.py          # Model layer tests
│   ├── test_utils.py           # Utility function tests
│   └── test_calculations.py    # Financial calculation tests
├── integration/                 # Integration tests
│   ├── __init__.py
│   ├── test_invoice_flow.py    # End-to-end invoice generation
│   ├── test_estimate_flow.py   # End-to-end estimate generation
│   └── test_database_ops.py    # Database operation tests
└── functional/                  # Functional tests
    ├── __init__.py
    ├── test_routes.py          # Flask route tests
    └── test_api.py             # API endpoint tests
```

## Test Categories

### Unit Tests (`unit/`)
- Test individual functions and methods in isolation
- Mock external dependencies
- Fast execution
- High coverage of edge cases

### Integration Tests (`integration/`)
- Test interaction between components
- Use real database (test database)
- Test complete workflows
- Verify data flow between layers

### Functional Tests (`functional/`)
- Test application behavior from user perspective
- Test HTTP endpoints
- Test complete user scenarios
- Use Flask test client

## Running Tests

### Prerequisites
```bash
# Activate the conda environment
conda activate invoice_generator

# Ensure you're in the project root directory
cd /path/to/invoice_generator
```

### Run All Tests
```bash
# Run all tests with verbose output
python -m unittest discover -s tests -v

# Run tests with coverage report
python -m coverage run -m unittest discover -s tests
python -m coverage report -m
```

### Run Specific Test Categories
```bash
# Run only unit tests
python -m unittest discover -s tests/unit -v

# Run only integration tests
python -m unittest discover -s tests/integration -v

# Run only functional tests
python -m unittest discover -s tests/functional -v
```

### Run Specific Test Files
```bash
# Run specific test file
python -m unittest tests.unit.test_calculations -v

# Run specific test class
python -m unittest tests.unit.test_calculations.CalculationTests -v

# Run specific test method
python -m unittest tests.unit.test_calculations.CalculationTests.test_iva_calculation -v
```

## Test Standards and Best Practices

### 1. File Naming Convention
- Test files: `test_<module_name>.py`
- Test classes: `<ModuleName>Tests` or `Test<ModuleName>`
- Test methods: `test_<specific_behavior>`

### 2. Test Class Structure
```python
class CalculationTests(TestBase):
    """Tests for financial calculation functions."""

    def setUp(self):
        """Set up test fixtures before each test method."""
        super().setUp()
        # Test-specific setup

    def tearDown(self):
        """Clean up after each test method."""
        # Test-specific cleanup
        super().tearDown()

    def test_iva_calculation_with_standard_rate(self):
        """Test IVA calculation with standard 21% rate."""
        # Arrange
        subtotal = 100.0
        iva_rate = 0.21

        # Act
        result = calculate_iva(subtotal, iva_rate)

        # Assert
        self.assertEqual(result, 21.0)
        self.assertIsInstance(result, float)
```

### 3. Test Method Structure (AAA Pattern)
- **Arrange**: Set up test data and conditions
- **Act**: Execute the function/method being tested
- **Assert**: Verify the results

### 4. Assertions Guidelines
- Use specific assertions: `assertEqual`, `assertIsNone`, `assertRaises`
- Include descriptive failure messages
- Test both positive and negative cases
- Test edge cases and boundary conditions

### 5. Test Data Management
- Use fixtures for reusable test data
- Create test-specific data in `setUp()` methods
- Clean up test data in `tearDown()` methods
- Use temporary databases for integration tests

### 6. Documentation Standards
- Every test class needs a docstring explaining its purpose
- Every test method needs a docstring describing what it tests
- Use clear, descriptive test method names
- Document any complex test setup or assertions

## Test Requirements

- Python 3.12+
- Flask
- SQLite3
- unittest (built-in)
- coverage (for coverage reports)

## Adding New Tests

### Step-by-Step Guide

1. **Determine Test Category**
   - Unit test: Testing a single function/method
   - Integration test: Testing component interaction
   - Functional test: Testing user-facing behavior

2. **Create Test File**
   ```bash
   # For unit tests
   touch tests/unit/test_new_feature.py

   # For integration tests
   touch tests/integration/test_new_workflow.py

   # For functional tests
   touch tests/functional/test_new_endpoint.py
   ```

3. **Use Test Template**
   ```python
   import unittest
   from tests.test_base import TestBase

   class NewFeatureTests(TestBase):
       """Tests for new feature functionality."""

       def setUp(self):
           """Set up test fixtures."""
           super().setUp()
           # Feature-specific setup

       def test_specific_behavior(self):
           """Test specific behavior of the feature."""
           # Arrange
           # Act
           # Assert
           pass
   ```

4. **Run Your Tests**
   ```bash
   # Run your new test file
   python -m unittest tests.unit.test_new_feature -v
   ```

5. **Verify Coverage**
   ```bash
   # Check coverage for your new tests
   python -m coverage run -m unittest tests.unit.test_new_feature
   python -m coverage report -m
   ```

## Continuous Integration

Tests should be run automatically on:
- Every commit to main branch
- Every pull request
- Before deployment

## Test Database

Integration tests use a separate test database to avoid affecting development data:
- Test database: `db/test_invoices.db`
- Automatically created and cleaned up during tests
- Isolated from main application database
