<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Invoice Generator 2025{% endblock %}</title>
    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/override.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/cursor-fix.css') }}">
    {% block extra_css %}{% endblock %}
    <style>
        /* Navigation bar styles */
        .navbar {
            background-color: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            padding: 0.75rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .navbar-brand {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .navbar-brand i {
            margin-right: 0.5rem;
            color: var(--accent-color);
        }

        .navbar-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-item {
            position: relative; /* Needed for dropdown positioning */
        }

        .nav-link, .dropdown-btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background-color: var(--accent-color);
            color: white;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none; /* For dropdown-btn */
            cursor: pointer; /* For dropdown-btn */
        }

        .nav-link:hover, .dropdown-btn:hover {
            background-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .nav-link i, .dropdown-btn i {
            margin-right: 0.5rem;
        }

        .nav-link.active, .dropdown-btn.active {
            background-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        /* Dropdown Content (Hidden by Default) */
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: var(--card-background);
            min-width: 180px; /* Adjusted width */
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000; /* Ensure dropdown is on top */
            border-radius: var(--border-radius);
            padding: 0.5rem 0;
            margin-top: 0.25rem; /* Small gap between button and dropdown */
        }

        /* Links inside the dropdown */
        .dropdown-content a {
            color: var(--text-primary);
            padding: 10px 16px; /* Adjusted padding */
            text-decoration: none;
            display: block;
            font-size: 0.9rem;
            white-space: nowrap; /* Prevent wrapping */
        }

        /* Change color of dropdown links on hover */
        .dropdown-content a:hover {
            background-color: var(--hover-background);
            color: var(--accent-color);
        }

        /* Show the dropdown menu on hover */
        .nav-item.dropdown:hover .dropdown-content {
            display: block;
        }

        /* Style the caret icon */
        .dropdown-btn .fa-caret-down {
            margin-left: 0.5rem;
        }

        /* Year selector - existing styles */
        .year-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 1.5rem 0;
        }

        .year-selector label {
            font-weight: 500;
            color: var(--text-secondary);
        }

        .year-selector select {
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            background-color: var(--input-background);
            color: var(--text-primary);
            font-size: 1rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle" id="theme-toggle">
        <i class="fas fa-moon"></i>
    </div>

    <nav class="navbar">
        <a class="navbar-brand" href="{{ url_for('index') }}">
            <i class="fas fa-file-invoice-dollar"></i> Invoice Generator 2025
        </a>
        <div class="navbar-nav">
            <div class="nav-item">
                <a class="nav-link {% if request.endpoint == 'index' and request.args.get('doc_type') != 'estimate' and request.args.get('doc_type') != 'invoice' %}active{% endif %}" href="{{ url_for('index') }}">
                    <i class="fas fa-home"></i> Home
                </a>
            </div>
            <div class="nav-item dropdown">
                <button class="dropdown-btn {% if request.endpoint == 'index' and request.args.get('doc_type') == 'invoice' or request.endpoint == 'all_invoices' %}active{% endif %}">
                    <i class="fas fa-file-invoice"></i> Invoices <i class="fas fa-caret-down"></i>
                </button>
                <div class="dropdown-content">
                    <a href="{{ url_for('index') }}?doc_type=invoice">New Invoice</a>
                    <a href="{{ url_for('all_invoices') }}">View All Invoices</a>
                </div>
            </div>
            <div class="nav-item dropdown">
                <button class="dropdown-btn {% if request.endpoint == 'index' and request.args.get('doc_type') == 'estimate' or request.endpoint == 'list_estimates' %}active{% endif %}">
                    <i class="fas fa-file-alt"></i> Estimates <i class="fas fa-caret-down"></i>
                </button>
                <div class="dropdown-content">
                    <a href="{{ url_for('index') }}?doc_type=estimate">New Estimate</a>
                    <a href="{{ url_for('list_estimates') }}">View All Estimates</a>
                </div>
            </div>
            <div class="nav-item">
                <a class="nav-link {% if request.endpoint == 'manage_clients' %}active{% endif %}" href="{{ url_for('manage_clients') }}">
                    <i class="fas fa-users"></i> Clients
                </a>
            </div>
            <div class="nav-item">
                <a class="nav-link {% if request.endpoint == 'manage_services' %}active{% endif %}" href="{{ url_for('manage_services') }}">
                    <i class="fas fa-concierge-bell"></i> Services
                </a>
            </div>
            <div class="nav-item dropdown">
                <button class="dropdown-btn {% if request.endpoint and request.endpoint in ['financial_summary', 'expenses_page', 'incomes_page', 'import_expenses'] %}active{% endif %}">
                    <i class="fas fa-chart-line"></i> Finance <i class="fas fa-caret-down"></i>
                </button>
                <div class="dropdown-content">
                    <a href="{{ url_for('financial_summary') }}">Financial Summary</a>
                    <a href="{{ url_for('import_expenses') }}">Import Expenses</a>
                    <a href="{{ url_for('expenses_page') }}">View Expenses</a>
                    <a href="{{ url_for('incomes_page') }}">View Incomes</a>
                </div>
            </div>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        <i class="fas {% if category == 'error' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %}"></i>
                        {{ message }}
                        <button type="button" class="alert-close" onclick="this.parentElement.style.display='none'">&times;</button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
