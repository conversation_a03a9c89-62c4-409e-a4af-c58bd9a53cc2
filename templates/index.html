{% extends "base.html" %}

{% block title %}Invoice & Estimate Generator 2025{% endblock %}

{% block extra_css %}
<style>
/* Bento Box Layout */
.bento-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-auto-rows: minmax(100px, auto);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.bento-item {
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
}

.bento-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.bento-item.show {
    opacity: 1;
    transform: translateY(0);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group-half {
    flex: 1;
    margin-bottom: 0;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-background);
    color: var(--text-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.2);
}

/* Selection Buttons */
.selection-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.selection-button {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-background);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.selection-button:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.selection-button.selected {
    border-color: var(--accent-color);
    background-color: rgba(var(--accent-color-rgb), 0.1);
}

.selection-button input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.selection-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.client-icon {
    background-color: #3498db; /* Blue */
}

.service-icon {
    background-color: #2ecc71; /* Green */
}

.selection-content {
    flex: 1;
}

.selection-title {
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.selection-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.custom-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: relative;
    height: 20px;
    width: 20px;
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.5rem;
    transition: all 0.2s ease;
}

.custom-checkbox:hover input ~ .checkmark {
    border-color: var(--accent-color);
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-checkbox .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    background-color: var(--accent-color);
    color: white;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-primary {
    background-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-block {
    display: block;
    width: 100%;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: 600;
    color: var(--text-secondary);
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(var(--accent-color-rgb), 0.05);
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-btn i {
    margin-right: 0.25rem;
}

.action-btn-view {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
}

.action-btn-view:hover {
    background-color: var(--primary-color);
    color: white;
}

.action-btn-delete {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

.action-btn-delete:hover {
    background-color: var(--danger-color);
    color: white;
}

/* Styles for Document Type Switcher */
.document-type-switcher {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0.5rem;
    background-color: var(--input-background);
    border-radius: var(--border-radius);
}

.doc-type-label {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.doc-type-label:hover {
    border-color: var(--accent-color);
}

.doc-type-label.selected {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.doc-type-label input[type="radio"] {
    position: absolute;
    opacity: 0;
}

/* Styles for estimate specific fields (initially hidden) */
.estimate-fields {
    display: none; /* Hidden by default, shown by JS */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .selection-container {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .checkbox-group {
        flex-direction: column;
        gap: 1rem;
    }

    .mobile-hide {
        display: none;
    }
}

/* Animation Delay for Bento Items */
.bento-item:nth-child(1) { transition-delay: 0.1s; }
.bento-item:nth-child(2) { transition-delay: 0.2s; }
.bento-item:nth-child(3) { transition-delay: 0.3s; }
.bento-item:nth-child(4) { transition-delay: 0.4s; }
.bento-item:nth-child(5) { transition-delay: 0.5s; }
</style>
{% endblock %}

{% block content %}
<div class="bento-grid">
    <div class="bento-item" style="grid-column: span 8;">
        <div class="card-header">
            <h2 class="card-title" id="form-title">Generate New Document</h2>
        </div>
        <div class="card-body">
            <form id="document-form" action="/generate_invoice" method="post">
                <!-- Document Type Switcher -->
                <div class="form-group">
                    <label class="form-label">Document Type</label>
                    <div class="document-type-switcher">
                        <label class="doc-type-label selected" id="doc-type-invoice-label">
                            <input type="radio" name="document_type" value="invoice" checked> Invoice
                        </label>
                        <label class="doc-type-label" id="doc-type-estimate-label">
                            <input type="radio" name="document_type" value="estimate"> Estimate
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Select Client</label>
                    <div class="selection-container" id="client-selection">
                        {% for client in clients %}
                        <label class="selection-button {% if client[0] == last_client_id %}selected{% endif %}">
                            <input type="radio" name="client_id" value="{{ client[0] }}" {% if client[0] == last_client_id %}checked{% endif %}>
                            <div class="selection-icon client-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="selection-content">
                                <h3 class="selection-title">{{ client[1] }}</h3>
                                <p class="selection-subtitle">{{ client[4] }}</p>
                            </div>
                        </label>
                        {% endfor %}
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Select Service</label>
                    <div class="selection-container" id="service-selection">
                        {% for service in services %}
                        <label class="selection-button {% if service[0] == last_service_id %}selected{% endif %}">
                            <input type="radio" name="service_id" value="{{ service[0] }}" {% if service[0] == last_service_id %}checked{% endif %}>
                            <div class="selection-icon service-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="selection-content">
                                <h3 class="selection-title">{{ service[1] }}</h3>
                                <p class="selection-subtitle">{{ currency_symbol }}{{ "%.2f"|format(service[2]) }} / {{ service[3] }}</p>
                            </div>
                        </label>
                        {% endfor %}
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group form-group-half">
                        <label class="form-label" for="quantity">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1" step="1">
                    </div>
                    <div class="form-group form-group-half">
                        <label class="form-label" for="issue_date">Issue Date</label> <!-- Changed label -->
                        <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ today_date }}">
                    </div>
                </div>

                <!-- Estimate Specific Fields -->
                <div class="estimate-fields">
                    <div class="form-row">
                        <div class="form-group form-group-half">
                            <label class="form-label" for="valid_until_date">Valid Until</label>
                            <input type="date" class="form-control" id="valid_until_date" name="valid_until_date">
                        </div>
                        <div class="form-group form-group-half">
                            <label class="form-label" for="irpf_rate">IRPF Rate (%)</label>
                            <select class="form-control" id="irpf_rate" name="irpf_rate">
                                <option value="0">0%</option>
                                <option value="0.07">7%</option>
                                <option value="0.15" selected>15%</option>
                                <option value="0.21">21%</option>
                                <!-- Add other common rates or allow custom input if needed -->
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="estimate_notes">Notes</label>
                        <textarea class="form-control" id="estimate_notes" name="estimate_notes" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="estimate_terms">Terms</label>
                        <textarea class="form-control" id="estimate_terms" name="estimate_terms" rows="3"></textarea>
                    </div>
                </div>

                <div class="form-group" id="tax-options-invoice"> <!-- ID for invoice tax options -->
                    <label class="form-label">Tax Options (Invoice)</label>
                    <div class="checkbox-group">
                        <label class="custom-checkbox">
                            <input type="checkbox" name="apply_iva" value="1" checked> <!-- Checked by default -->
                            <span class="checkmark"></span>
                            Apply VAT (21%)
                        </label>
                        <label class="custom-checkbox" id="irpf-checkbox-invoice">
                            <input type="checkbox" name="apply_irpf" value="1" checked> <!-- Checked by default -->
                            <span class="checkmark"></span>
                            Apply IRPF (15%)
                        </label>
                    </div>
                </div>
                
                <div class="form-group estimate-fields"> <!-- VAT for estimates, IRPF is handled by dropdown -->
                    <label class="form-label">Tax Options (Estimate)</label>
                     <div class="checkbox-group">
                        <label class="custom-checkbox">
                            <input type="checkbox" name="apply_iva_estimate" value="1" checked> <!-- Checked by default -->
                            <span class="checkmark"></span>
                            Apply VAT (21%)
                        </label>
                    </div>
                </div>


                <button type="submit" class="btn btn-primary btn-block" id="generate-button">Generate Invoice</button>
            </form>
        </div>
    </div>

    <div class="bento-item" style="grid-column: span 4;">
        <div class="card-header">
            <h2 class="card-title">Recent Documents</h2>
        </div>
        <div class="card-body">
            {% if recent_invoices or recent_estimates %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Number</th>
                                <th>Client</th>
                                <th>Date</th>
                                <th>Total</th>
                                <th>Type</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in recent_invoices %}
                            <tr>
                                <td>{{ invoice[1] }}</td> {# invoice_number #}
                                <td>{{ invoice[3] }}</td> {# client_name #}
                                <td>{{ invoice[2] }}</td> {# date #}
                                <td>{{ invoice[10] }}{{ "%.2f"|format(invoice[13]) }}</td> {# currency_symbol + total_amount #}
                                <td><span class="badge badge-invoice">Invoice</span></td>
                                <td>
                                    <a href="{{ url_for('view_invoice', invoice_number=invoice[1]) }}" class="action-btn action-btn-view"><i class="fas fa-eye"></i></a>
                                    <a href="#" onclick="confirmDelete('invoice', '{{ invoice[1] }}')" class="action-btn action-btn-delete"><i class="fas fa-trash"></i></a>
                                </td>
                            </tr>
                            {% endfor %}
                            {% for estimate in recent_estimates %}
                            <tr>
                                <td>{{ estimate[1] }}</td> {# estimate_number #}
                                <td>{{ estimate[18] }}</td> {# client_name (assuming e.*, client_name, service_name, currency_symbol) #}
                                <td>{{ estimate[4] }}</td> {# issue_date #}
                                <td>{{ estimate[20] }}{{ "%.2f"|format(estimate[13]) }}</td> {# currency_symbol + total_amount #}
                                <td><span class="badge badge-estimate">Estimate</span></td>
                                <td>
                                    <a href="{{ url_for('view_estimate', estimate_number=estimate[1]) }}" class="action-btn action-btn-view"><i class="fas fa-eye"></i></a>
                                    <a href="#" onclick="confirmDelete('estimate', '{{ estimate[1] }}')" class="action-btn action-btn-delete"><i class="fas fa-trash"></i></a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p>No recent documents found.</p>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Confirm delete function
    function confirmDelete(docType, docNumber) { // Added docType
        if (confirm('Are you sure you want to delete ' + docType + ' ' + docNumber + '? This action cannot be undone.')) {
            window.location.href = '/delete_' + docType + '/' + docNumber;
        }
    }

    // Document type switcher logic
    const invoiceFields = document.querySelectorAll('.invoice-fields');
    const estimateFields = document.querySelectorAll('.estimate-fields');
    const docTypeLabels = document.querySelectorAll('.doc-type-label');

    document.getElementById('doc-type-invoice-label').addEventListener('click', () => {
        invoiceFields.forEach(field => field.style.display = 'block');
        estimateFields.forEach(field => field.style.display = 'none');
        docTypeLabels.forEach(label => label.classList.remove('selected'));
        document.getElementById('doc-type-invoice-label').classList.add('selected');
        document.getElementById('doc-type-estimate-label').classList.remove('selected');
        document.getElementById('form-title').innerText = 'Generate New Invoice';
        document.getElementById('generate-button').innerText = 'Generate Invoice';
    });

    document.getElementById('doc-type-estimate-label').addEventListener('click', () => {
        invoiceFields.forEach(field => field.style.display = 'none');
        estimateFields.forEach(field => field.style.display = 'block');
        docTypeLabels.forEach(label => label.classList.remove('selected'));
        document.getElementById('doc-type-estimate-label').classList.add('selected');
        document.getElementById('doc-type-invoice-label').classList.remove('selected');
        document.getElementById('form-title').innerText = 'Generate New Estimate';
        document.getElementById('generate-button').innerText = 'Generate Estimate';
    });

    // Initial setup
    document.addEventListener('DOMContentLoaded', () => {
        document.getElementById('doc-type-invoice-label').click();
    });
</script>
{% endblock %}
